import { docsMetadata } from "@/lib/utils";

export const metadata = docsMetadata({
  title: "Express",
  description: "Adapter to integrate UploadThing into your Express application",
  category: "Backend Adapters",
});

# Getting started with Express

> Added in `v5.6`

## Package Setup

### Install the package

```sh npm2yarn
npm install uploadthing
```

### Add env variables

<Note>
  If you don't already have a uploadthing secret key, [sign
  up](https://uploadthing.com/sign-in) and create one from the
  [dashboard!](https://uploadthing.com/dashboard)
</Note>

```bash
UPLOADTHING_TOKEN=... # A token for interacting with the SDK
```

## Set Up A FileRouter

### Creating your first FileRoute

All files uploaded to uploadthing are associated with a FileRoute. The following
is a very minimalistic example, with a single FileRoute "imageUploader". Think
of a FileRoute similar to an endpoint, it has:

- Permitted types ["image", "video", etc]
- Max file size
- How many files are allowed to be uploaded
- (Optional) `input` validation to validate client-side data sent to the route
- (Optional) `middleware` to authenticate and tag requests
- `onUploadComplete` callback for when uploads are completed

To get full insight into what you can do with the FileRoutes, please refer to
the [File Router API](/file-routes).

```ts {{ title: "src/uploadthing.ts" }}
import { createUploadthing, type FileRouter } from "uploadthing/express";

const f = createUploadthing();

export const uploadRouter = {
  // Define as many FileRoutes as you like, each with a unique routeSlug
  imageUploader: f({
    image: {
      /**
       * For full list of options and defaults, see the File Route API reference
       * @see https://docs.uploadthing.com/file-routes#route-config
       */
      maxFileSize: "4MB",
      maxFileCount: 1,
    },
  }).onUploadComplete((data) => {
    console.log("upload completed", data);
  }),
} satisfies FileRouter;

export type OurFileRouter = typeof uploadRouter;
```

### Create an API route using the FileRouter

<Note>
  File path here doesn't matter, you can serve this from any route. We recommend
  serving it from `/api/uploadthing`.
</Note>

```ts {{ title: "src/index.ts" }}
import express from "express";

import { createRouteHandler } from "uploadthing/express";

import { uploadRouter } from "./uploadthing";

const app = express();

app.use(
  "/api/uploadthing",
  createRouteHandler({
    router: uploadRouter,
    config: { ... },
  }),
);
```

> See configuration options in
> [server API reference](/api-reference/server#create-route-handler)

### Use the FileRouter in your app

Client side usage differs ever so slightly from the fullstack framework setups
when using a separate backend server. You'll need to set the URL of your server
when you generate the components and helpers.

```tsx
import { generateUploadButton } from "@uploadthing/react";

export const UploadButton = generateUploadButton({
  url: "https://your-server.com/api/uploadthing",
});
// ...
```

<Note>
  Please note that you might need to setup some CORS rules on your server to
  allow the client to make requests to the server.
</Note>

For the remaining usage, please refer to client side examples of the fullstack
framework guides:

- [Next.js](/getting-started/appdir#create-the-upload-thing-components)
- [Solid.js](/getting-started/solid#creating-the-upload-thing-components)
- [Vue](https://github.com/pingdotgg/uploadthing/tree/main/examples/backend-adapters/client-vue)
- [Svelte](/getting-started/svelte#creating-the-upload-thing-helpers)

or check out the full API reference:

- [`@uploadthing/react`](/api-reference/react)
- [`uploadthing/client`](/api-reference/client)
