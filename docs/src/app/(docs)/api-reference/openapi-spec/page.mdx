import { docsMetadata } from "@/lib/utils";

export const metadata = docsMetadata({
  title: "OpenAPI Specification",
  description:
    "The UploadThing REST API specification for developers building SDKs",
  category: "API Reference",
});

# UploadThing REST API Specification

You can use the UploadThing REST API to build SDKs for languages and frameworks
we don't natively support. The API is designed to be simple and easy to use. The
latest version of all endpoints are documented here.

import { Scalar } from "./scalar-server";

<Scalar />
