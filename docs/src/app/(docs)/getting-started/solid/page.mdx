import { docsMetadata } from "@/lib/utils";

export const metadata = docsMetadata({
  title: "SolidStart Setup",
  description: "Learn how to set up SolidStart with UploadThing",
  category: "Getting Started",
});

# Getting Started with SolidStart

## Package Setup

### Install the packages

```bash npm2yarn
npm install uploadthing @uploadthing/solid
```

### Add env variables

```bash
UPLOADTHING_TOKEN=... # A token for interacting with the SDK
```

<Note>
  If you don't already have a uploadthing token, [sign
  up](https://uploadthing.com/sign-in) and create one from the
  [dashboard!](https://uploadthing.com/dashboard)
</Note>

## Set Up A FileRouter

### Creating your first FileRoute

All files uploaded to uploadthing are associated with a FileRoute. The following
is a very minimalistic example, with a single FileRoute "imageUploader". Think
of a FileRoute similar to an endpoint, it has:

- Permitted types ["image", "video", etc]
- Max file size
- How many files are allowed to be uploaded
- (Optional) `input` validation to validate client-side data sent to the route
- (Optional) `middleware` to authenticate and tag requests
- `onUploadComplete` callback for when uploads are completed

To get full insight into what you can do with the FileRoutes, please refer to
the [File Router API](/file-routes).

```ts {{ title: "src/server/uploadthing.ts" }}
import { createUploadthing, UploadThingError } from "uploadthing/server";
import type { FileRouter } from "uploadthing/server";

const f = createUploadthing();

const auth = (req: Request) => ({ id: "fakeId" }); // Fake auth function

export const uploadRouter = {
  // Define as many FileRoutes as you like, each with a unique routeSlug
  imageUploader: f({
    image: {
      /**
       * For full list of options and defaults, see the File Route API reference
       * @see https://docs.uploadthing.com/file-routes#route-config
       */
      maxFileSize: "4MB",
      maxFileCount: 1,
    },
  })
    // Set permissions and file types for this FileRoute
    .middleware(async ({ req }) => {
      // This code runs on your server before upload
      const user = await auth(req);

      // If you throw, the user will not be able to upload
      if (!user) throw new UploadThingError("Unauthorized");

      // Whatever is returned here is accessible in onUploadComplete as `metadata`
      return { userId: user.id };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Upload complete for userId:", metadata.userId);

      console.log("file url", file.ufsUrl);

      // !!! Whatever is returned here is sent to the clientside `onClientUploadComplete` callback
      return { uploadedBy: metadata.userId };
    }),
} satisfies FileRouter;

export type UploadRouter = typeof uploadRouter;
```

### Create an API route using the FileRouter

<Note>
  File path here doesn't matter, you can serve this from any route. We recommend
  serving it from `/api/uploadthing`.
</Note>

```ts {{ title: "src/routes/api/uploadthing.ts" }}
import type { APIEvent } from "@solidjs/start/server";

import { createRouteHandler } from "uploadthing/server";

import { uploadRouter } from "~/server/uploadthing";

const handler = createRouteHandler({
  router: uploadRouter,
});

export const GET = (event: APIEvent) => handler(event.request);
export const POST = (event: APIEvent) => handler(event.request);
```

> See configuration options in
> [server API reference](/api-reference/server#create-route-handler)

## Create The UploadThing Components

We provide components to make uploading easier. We highly recommend re-exporting
them with the types assigned, but you CAN import the components individually
from `@uploadthing/solid` instead.

```ts {{ title: "src/utils/uploadthing.ts" }}
import {
  generateSolidHelpers,
  generateUploadButton,
  generateUploadDropzone,
} from "@uploadthing/solid";

import type { UploadRouter } from "~/server/uploadthing";

// URL is only needed for server side rendering, or when your router
// is deployed on a different path than `/api/uploadthing`
const url = `http://localhost:${process.env.PORT ?? 3000}`;

export const UploadButton = generateUploadButton<UploadRouter>({ url });
export const UploadDropzone = generateUploadDropzone<UploadRouter>({ url });
export const { createUploadThing } = generateSolidHelpers<UploadRouter>({
  url,
});
```

<Note>
  To learn more about the components, check out the [solid API
  Reference](/api-reference/solid).
</Note>

### Add UploadThing's Styles

<Tabs tabs={["Tailwind v3", "Tailwind v4", "Not Tailwind"]}>
    <Tab>
      Wrap your Tailwind config with the `withUt` helper. You can learn more about our
      Tailwind helper in the ["Theming" page](/concepts/theming#theming-with-tailwind-css)

      ```tsx
      import { withUt } from "uploadthing/tw";

      export default withUt({
        // Your existing Tailwind config
        content: ["./src/**/*.{ts,tsx,mdx}"],
        ...
      });
      ```

    </Tab>

    <Tab>
      If you're using Tailwind v4 with CSS configuration, you can use our plugin like this:

      ```css
      @import "tailwindcss";

      @import "uploadthing/tw/v4";
      @source "../node_modules/@uploadthing/solid/dist"; /** <-- depends on your project structure */
      ```

      You can learn more about our Tailwind helper in the ["Theming" page](/concepts/theming#theming-with-tailwind-css)
    </Tab>

    <Tab>
      Include our CSS file in the root layout to make sure the components look right!

      ```tsx {{ title: "src/app.tsx"}}
      import "@uploadthing/solid/styles.css";
      ```

    </Tab>

</Tabs>

## Mount A Button And Upload!

```tsx {{ title: "src/routes/index.tsx" }}
import { UploadButton } from "~/utils/uploadthing";

export default function Home() {
  return <UploadButton endpoint="imageUploader" />;
}
```

---

### 🎉 You're Done!

Want to customize the components? Check out the
["Theming" page](/concepts/theming)

Want to make your own components? Check out our
[useUploadThing hook](/api-reference/react#use-upload-thing)
