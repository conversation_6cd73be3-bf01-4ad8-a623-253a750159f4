{"name": "@uploadthing/react", "version": "7.3.2", "type": "module", "sideEffects": false, "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", "./styles.css": "./dist/index.css", ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./native": {"import": {"types": "./native/index.d.ts", "default": "./native/index.js"}, "require": {"types": "./native/index.d.cts", "default": "./native/index.cjs"}}, "./next-ssr-plugin": {"import": {"types": "./next-ssr-plugin/index.d.ts", "default": "./next-ssr-plugin/index.js"}, "require": {"types": "./next-ssr-plugin/index.d.cts", "default": "./next-ssr-plugin/index.cjs"}}}, "files": ["dist", "native", "next-ssr-plugin"], "publishConfig": {"access": "public"}, "scripts": {"build": "tsdown", "dev": "tsdown --no-clean", "clean": "git clean -xdf dist hooks native next-ssr-plugin node_modules", "lint": "eslint src test --max-warnings 0", "prepack": "bun ../../.github/replace-workspace-protocol.ts", "test": "vitest run", "typecheck": "tsc --noEmit"}, "dependencies": {"@uploadthing/shared": "workspace:*", "file-selector": "0.6.0"}, "peerDependencies": {"next": "*", "react": "^17.0.2 || ^18.0.0 || ^19.0.0", "uploadthing": "^7.2.0"}, "peerDependenciesMeta": {"next": {"optional": true}}, "devDependencies": {"@types/node": "^22.10.0", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@uploadthing/eslint-config": "workspace:*", "@uploadthing/tsconfig": "workspace:*", "@uploadthing/vitest-config": "workspace:*", "@vitest/browser": "3.2.4", "@vitest/coverage-istanbul": "3.2.4", "concurrently": "^9.1.2", "eslint": "9.25.1", "msw": "2.7.5", "next": "15.3.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "^3.4.16", "tsdown": "0.12.1", "typescript": "5.8.3", "uploadthing": "workspace:*", "vitest": "3.2.4", "vitest-browser-react": "0.3.0", "wait-on": "^8.0.1", "zod": "^3.24.1"}}