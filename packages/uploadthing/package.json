{"name": "uploadthing", "version": "7.7.3", "type": "module", "sideEffects": false, "engines": {"node": ">=18.13.0"}, "license": "MIT", "exports": {"./package.json": "./package.json", "./client": {"import": {"types": "./client/index.d.ts", "default": "./client/index.js"}, "require": {"types": "./client/index.d.cts", "default": "./client/index.cjs"}}, "./client-future": {"import": {"types": "./client-future/index.d.ts", "default": "./client-future/index.js"}, "require": {"types": "./client-future/index.d.cts", "default": "./client-future/index.cjs"}}, "./server": {"import": {"types": "./server/index.d.ts", "default": "./server/index.js"}, "require": {"types": "./server/index.d.cts", "default": "./server/index.cjs"}}, "./next": {"import": {"types": "./next/index.d.ts", "default": "./next/index.js"}, "require": {"types": "./next/index.d.cts", "default": "./next/index.cjs"}}, "./next-legacy": {"import": {"types": "./next-legacy/index.d.ts", "default": "./next-legacy/index.js"}, "require": {"types": "./next-legacy/index.d.cts", "default": "./next-legacy/index.cjs"}}, "./effect-platform": {"import": {"types": "./effect-platform/index.d.ts", "default": "./effect-platform/index.js"}, "require": {"types": "./effect-platform/index.d.cts", "default": "./effect-platform/index.cjs"}}, "./tw": {"browser": "./tw/index.browser.js", "import": {"types": "./tw/index.d.ts", "default": "./tw/index.js"}, "require": {"types": "./tw/index.d.cts", "default": "./tw/index.cjs"}}, "./tw/v4": "./tw/v4.css", "./fastify": {"import": {"types": "./fastify/index.d.ts", "default": "./fastify/index.js"}, "require": {"types": "./fastify/index.d.cts", "default": "./fastify/index.cjs"}}, "./express": {"import": {"types": "./express/index.d.ts", "default": "./express/index.js"}, "require": {"types": "./express/index.d.cts", "default": "./express/index.cjs"}}, "./h3": {"import": {"types": "./h3/index.d.ts", "default": "./h3/index.js"}, "require": {"types": "./h3/index.d.cts", "default": "./h3/index.cjs"}}, "./remix": {"import": {"types": "./remix/index.d.ts", "default": "./remix/index.js"}, "require": {"types": "./remix/index.d.cts", "default": "./remix/index.cjs"}}, "./types": {"types": "./types/index.d.ts", "default": "./types/index.js"}}, "files": ["client", "client-future", "dist", "effect-platform", "express", "fastify", "h3", "next", "next-legacy", "remix", "server", "types", "tw"], "publishConfig": {"access": "public"}, "scripts": {"lint": "eslint src test --max-warnings 0", "build": "tsdown", "clean": "git clean -xdf client express fastify h3 internal next next-legacy server tw node_modules", "dev": "tsdown --no-clean", "prepack": "bun ../../.github/replace-workspace-protocol.ts", "test": "vitest run", "typecheck": "tsc --noEmit"}, "dependencies": {"@effect/platform": "0.85.2", "@standard-schema/spec": "1.0.0-beta.4", "@uploadthing/mime-types": "workspace:*", "@uploadthing/shared": "workspace:*", "effect": "3.16.8"}, "devDependencies": {"@effect/vitest": "0.23.8", "@remix-run/server-runtime": "^2.12.0", "@types/body-parser": "^1.19.5", "@types/express": "^5.0.0", "@types/express-serve-static-core": "^5.0.3", "@types/react": "19.1.2", "@uploadthing/eslint-config": "workspace:*", "@uploadthing/tsconfig": "workspace:*", "@uploadthing/vitest-config": "workspace:*", "@vitest/coverage-istanbul": "3.2.4", "body-parser": "^1.20.2", "eslint": "9.25.1", "express": "^5.0.1", "fastify": "^5.2.0", "h3": "^1.13.0", "msw": "2.7.5", "next": "15.3.1", "solid-js": "^1.9.3", "tailwindcss": "^3.4.16", "tsdown": "0.12.1", "typescript": "5.8.3", "valibot": "1.0.0-beta.9", "vitest": "3.2.4", "vue": "^3.4.21", "wait-on": "^8.0.1", "zod": "^3.24.1"}, "peerDependencies": {"express": "*", "h3": "*", "tailwindcss": "^3.0.0 || ^4.0.0-beta.0"}, "peerDependenciesMeta": {"next": {"optional": true}, "express": {"optional": true}, "fastify": {"optional": true}, "h3": {"optional": true}, "tailwindcss": {"optional": true}}}