# @uploadthing/mime-types

## 0.3.5

### Patch Changes

- [#1193](https://github.com/pingdotgg/uploadthing/pull/1193)
  [`ff575c0`](https://github.com/pingdotgg/uploadthing/commit/ff575c0fdc4a6f6b16c0534657083bd2c49d51e8)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - chore: update
  internal effect version

## 0.3.4

### Patch Changes

- [#1108](https://github.com/pingdotgg/uploadthing/pull/1108)
  [`b2de189`](https://github.com/pingdotgg/uploadthing/commit/b2de189ba88024eb141945eb034ccd547f946595)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - chore: more
  relaxed peer dep requirements between uploadthing packages

## 0.3.3

### Patch Changes

- [#1103](https://github.com/pingdotgg/uploadthing/pull/1103)
  [`fdc68ba`](https://github.com/pingdotgg/uploadthing/commit/fdc68bae1f030fe1a3d3dbb06cc219f9612faf82)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - fix: tidy up
  ranges for peer dependencies

## 0.3.2

### Patch Changes

- [#1013](https://github.com/pingdotgg/uploadthing/pull/1013)
  [`7ae1ed9`](https://github.com/pingdotgg/uploadthing/commit/7ae1ed9a8d386a42f3f1b3cda41859f234f2e560)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - fix: support
  legacy module resolutions to support Expo

## 0.3.1

### Patch Changes

- [#1022](https://github.com/pingdotgg/uploadthing/pull/1022)
  [`e2df2a2`](https://github.com/pingdotgg/uploadthing/commit/e2df2a29a9674ebf62091ebfd87706e084e5046b)
  Thanks [@christian-bromann](https://github.com/christian-bromann)! -
  feat(@uploadthing/mime-types) add application/yaml as mime type

## 0.3.0

### Minor Changes

- [#943](https://github.com/pingdotgg/uploadthing/pull/943)
  [`2efa047`](https://github.com/pingdotgg/uploadthing/commit/2efa047127890bdf50ab5312ff9660662e099162)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - chore: bump
  mime-types

## 0.2.10

### Patch Changes

- [#806](https://github.com/pingdotgg/uploadthing/pull/806)
  [`5e6e64c`](https://github.com/pingdotgg/uploadthing/commit/5e6e64c53ac9765ceee4bb758a48e08eabb36d14)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - set
  `sideEffects: false` for better tree-shaking

## 0.2.9

### Patch Changes

- [#773](https://github.com/pingdotgg/uploadthing/pull/773)
  [`6906254`](https://github.com/pingdotgg/uploadthing/commit/690625458338a70df5927f1d2405de0de4a58d8f)
  Thanks [@Bodykudo](https://github.com/Bodykudo)! - Extend supported filetypes
  by adding dcm (DICOM files) mime type

## 0.2.8

### Patch Changes

- [#758](https://github.com/pingdotgg/uploadthing/pull/758)
  [`e637c43`](https://github.com/pingdotgg/uploadthing/commit/e637c43d203b72dabfeb17755b6d22d03c05ea3c)
  Thanks [@ertucode](https://github.com/ertucode)! - dont initialize exported
  value in the global scope to improve treeshaking

- [#764](https://github.com/pingdotgg/uploadthing/pull/764)
  [`5efcdda`](https://github.com/pingdotgg/uploadthing/commit/5efcddafe9aa11993e16824dae4822bd7a8c8199)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - chore: update
  jpeg file extensions

## 0.2.7

### Patch Changes

- [#740](https://github.com/pingdotgg/uploadthing/pull/740)
  [`0069ead`](https://github.com/pingdotgg/uploadthing/commit/0069eadbffd90db29df1966eae4f0a85aa3a8490)
  Thanks [@kaspnilsson](https://github.com/kaspnilsson)! - Add support for
  'model/step' MIME type

## 0.2.6

### Patch Changes

- [`d7c2018`](https://github.com/pingdotgg/uploadthing/commit/d7c2018f62c9e1ee9e0c11514e4ff3f28cc5e939)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - fix bad release
  with `workspace:` protocol in published distributions

## 0.2.5

### Patch Changes

- [#655](https://github.com/pingdotgg/uploadthing/pull/655)
  [`0adc3b8`](https://github.com/pingdotgg/uploadthing/commit/0adc3b8df67ea5c4a94db736d0aff1b489979393)
  Thanks [@ecwyne](https://github.com/ecwyne)! - add `audio/x-gsm` mime-type

## 0.2.4

### Patch Changes

- [#620](https://github.com/pingdotgg/uploadthing/pull/620)
  [`0ee53b5`](https://github.com/pingdotgg/uploadthing/commit/0ee53b553e3304444d5fcf35fdfbd18cc317e668)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - fix(cjs
  bundling): force client splitting in .cjs output files

## 0.2.3

### Patch Changes

- [`352eea6`](https://github.com/pingdotgg/uploadthing/commit/352eea651218501f6535420287e8d8170faafec7)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - chore: refactor
  bundling #579

## 0.2.2

### Patch Changes

- [#448](https://github.com/pingdotgg/uploadthing/pull/448)
  [`eb5f96d`](https://github.com/pingdotgg/uploadthing/commit/eb5f96dc06a81ecb4b1f7ee3d0ba259ebdfee7d1)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - fix: remove
  traces of leaking zod dependency

## 0.2.1

### Patch Changes

- [#281](https://github.com/pingdotgg/uploadthing/pull/281)
  [`86d72be`](https://github.com/pingdotgg/uploadthing/commit/86d72be25c794aadcfe55a08095b487a782e2dc8)
  Thanks [@Mr0Bread](https://github.com/Mr0Bread)! - fix: added settings to
  support cjs imports

## 0.2.0

### Minor Changes

- [#136](https://github.com/pingdotgg/uploadthing/pull/136)
  [`23a9e19`](https://github.com/pingdotgg/uploadthing/commit/23a9e19702a51dec2ace869f47211f883d888d74)
  Thanks [@juliusmarminge](https://github.com/juliusmarminge)! - inhouse a fork
  of mime-types that's edge-ready
