{"name": "@uploadthing/shared", "version": "7.1.9", "type": "module", "sideEffects": false, "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.js", "typings": "dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"lint": "eslint src test --max-warnings 0", "build": "tsdown", "clean": "git clean -xdf dist node_modules", "dev": "tsdown --no-clean", "prepack": "bun ../../.github/replace-workspace-protocol.ts", "test": "vitest run", "typecheck": "tsc --noEmit"}, "dependencies": {"@uploadthing/mime-types": "workspace:*", "effect": "3.16.8", "sqids": "^0.3.0"}, "devDependencies": {"@effect/vitest": "0.23.8", "@types/react": "19.1.2", "@uploadthing/eslint-config": "workspace:", "@uploadthing/tsconfig": "workspace:", "@uploadthing/vitest-config": "workspace:", "@vitest/coverage-istanbul": "3.2.4", "eslint": "9.25.1", "react": "19.1.0", "solid-js": "^1.9.3", "tsdown": "0.12.1", "typescript": "5.8.3", "vitest": "3.2.4", "vue": "^3.4.21", "wait-on": "^8.0.1"}}