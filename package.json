{"name": "uploadthing-turbo", "private": true, "type": "module", "engines": {"node": ">=22.x", "pnpm": "10.x"}, "packageManager": "pnpm@10.10.0", "scripts": {"build:all": "turbo run build", "build:vanilla": "turbo run build --filter uploadthing...", "build:react": "turbo run build --filter @uploadthing/react...", "build": "turbo run build --filter \"./packages/*\" --concurrency=15", "clean": "turbo run clean && git clean -xdf node_modules", "dev:all": "turbo watch dev", "dev": "turbo watch dev --filter \"./packages/*\"", "dev:vanilla": "turbo watch dev --filter uploadthing...", "dev:expo": "turbo watch dev --filter @uploadthing/expo...", "dev:react": "turbo watch dev --filter @uploadthing/react...", "dev:pg": "turbo watch dev --filter next-playground...", "dev:docs": "turbo watch dev --filter docs...", "lint": "turbo run lint && manypkg check", "format:check": "prettier --check .", "format": "prettier --write . --list-different", "test": "turbo run test --filter @uploadthing/* --filter uploadthing", "test:all": "turbo test", "test:watch": "turbo watch test --filter @uploadthing/* --filter uploadthing", "typecheck": "turbo run typecheck"}, "devDependencies": {"@actions/github": "^6.0.0", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.11", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@manypkg/cli": "^0.23.0", "@prettier/sync": "^0.5.2", "@types/bun": "^1.2.5", "@types/node": "^22.10.0", "playwright": "1.52.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.9", "turbo": "2.5.3-canary.1", "typescript": "5.8.3", "uploadthing": "workspace:*"}}